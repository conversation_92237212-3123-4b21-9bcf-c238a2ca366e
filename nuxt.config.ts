import tailwindcss from '@tailwindcss/vite'
import compression from 'vite-plugin-compression'
import { createGzip } from 'node:zlib'
import type { Plugin } from 'vite'

// 自定义开发环境压缩插件
function devCompressionPlugin(): Plugin {
  return {
    name: 'dev-compression',
    configureServer(server) {
      server.middlewares.use((req, res, next) => {
        const acceptEncoding = req.headers['accept-encoding'] || ''

        // 检查是否支持 gzip 且是 CSS/JS 文件
        if (acceptEncoding.includes('gzip') &&
            (req.url?.includes('.css') || req.url?.includes('.js'))) {

          const originalWrite = res.write
          const originalEnd = res.end
          const chunks: Buffer[] = []

          // 拦截响应数据
          res.write = function(chunk: any, encoding?: any) {
            if (chunk) {
              chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk, encoding))
            }
            return true
          }

          res.end = function(chunk?: any, encoding?: any): any {
            if (chunk) {
              chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk, encoding))
            }

            const data = Buffer.concat(chunks)

            // 只压缩大于 1KB 的内容
            if (data.length > 1024) {
              const gzip = createGzip()
              const compressed: Buffer[] = []

              gzip.on('data', (chunk) => compressed.push(chunk))
              gzip.on('end', () => {
                const compressedData = Buffer.concat(compressed)
                res.setHeader('Content-Encoding', 'gzip')
                res.setHeader('Content-Length', compressedData.length)
                res.setHeader('Vary', 'Accept-Encoding')
                originalEnd.call(res, compressedData)
              })

              gzip.end(data)
            } else {
              originalEnd.call(res, data)
            }
            return res
          }
        }

        next()
      })
    }
  }
}

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: false },
  debug: false,

  future: {
    compatibilityVersion: 4,
  },
  
  modules: [
    '@vueuse/nuxt',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/color-mode',
    '@nuxt/eslint',
    'nuxt-auth-utils',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/fonts',
  ],

  // * Note that this option will not override the default directories (~/components, ~/composables, ~/middleware, ~/utils).
  imports: {
    dirs: [
      '../stores',
      '../app/utils',
    ],
  },

  fonts: {
    providers: {
      google: false,
      googleicons: false,
    },
    priority: ['bunny', 'fontsource', 'local']
  },

  // vue: {
  //   compilerOptions: {
  //     isCustomElement: tag => ['iconify-icon'].includes(tag),
  //   },
  // },

  colorMode: {
    preference: 'dark', // default value of $colorMode.preference -> 'system'
    fallback: 'dark', // fallback value if not system preference found
    classSuffix: '',
  },

  // content: {
  //   // highlight: {
  //   //   // Theme used in all color schemes.
  //   //   theme: 'github-dark-high-contrast',
  //   //   // OR
  //   //   // theme: {
  //   //   //   // Default theme (same as single string)
  //   //   //   default: 'vitesse-dark',
  //   //   //   // Theme used if `html.dark`
  //   //   //   dark: 'github-dark-high-contrast',
  //   //   //   // Theme used if `html.sepia`
  //   //   //   sepia: 'monokai'
  //   //   // }
  //   // },
  // },

  runtimeConfig: {
    fromEmail: 'gomark.pro <<EMAIL>>',
    public: {
      brand: 'Gomark',
      contactEmail: '<EMAIL>',
      baseUrl: process.env.NODE_ENV === 'development' ? `http://localhost:${process.env.PORT ?? 3000}` : 'https://gomark.pro',
    },
  },

  // Build as SPA application
  // ssr: false,

  routeRules: {
    '/admin/**': { ssr: false },
    '/dashboard/**': { ssr: false },
    // '/api/**': { cors: true },
  },

  // hmr 的性能取决于 tailwindcss 的大小，而大小取决于页面使用 class 的多少以及 theme 的多少
  // 参考 nuxt-app，删除 ui 文件夹及 theme 等等，默认的 tailwindcss 仅 40 KB左右，hmr 就非常快
  css: ['~/assets/css/tailwind.css'],
  vite: {
    plugins: [
      tailwindcss(),
      // 开发环境启用实时 gzip 压缩
      devCompressionPlugin(),
      // 构建时启用 gzip 压缩
      compression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 1024, // 只压缩大于 1KB 的文件
        deleteOriginFile: false, // 保留原文件
        verbose: process.env.NODE_ENV === 'development', // 开发环境显示压缩信息
      }),
    ],
    server: {
      // 开发环境启用压缩
      middlewareMode: false,
    }
  },

  // 启用 gzip 压缩（包括开发环境）
  nitro: {
    compressPublicAssets: true,
    // 开发环境启用压缩中间件
    experimental: {
      wasm: true
    },
    // 开发环境配置
    devServer: {
      watch: []
    }
  },

  sourcemap: { server: false, client: false },

  eslint: {
    config: {
      stylistic: true,
    },
  },

  icon: {
    mode: 'svg',
    customCollections: [
      {
        prefix: 'custom',
        dir: './app/assets/custom-icons'
      },
    ],
  },
  
  shadcn: {
    prefix: '',
    componentDir: '@/components/ui',
  },
})
