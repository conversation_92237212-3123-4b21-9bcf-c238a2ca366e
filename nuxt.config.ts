import tailwindcss from '@tailwindcss/vite'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: false },
  debug: false,

  future: {
    compatibilityVersion: 4,
  },
  
  modules: [
    '@vueuse/nuxt',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/color-mode',
    '@nuxt/eslint',
    'nuxt-auth-utils',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/fonts',
  ],

  // * Note that this option will not override the default directories (~/components, ~/composables, ~/middleware, ~/utils).
  imports: {
    dirs: [
      '../stores',
      '../app/utils',
    ],
  },

  fonts: {
    providers: {
      google: false,
      googleicons: false,
    },
    priority: ['bunny', 'fontsource', 'local']
  },

  // vue: {
  //   compilerOptions: {
  //     isCustomElement: tag => ['iconify-icon'].includes(tag),
  //   },
  // },

  colorMode: {
    preference: 'dark', // default value of $colorMode.preference -> 'system'
    fallback: 'dark', // fallback value if not system preference found
    classSuffix: '',
  },

  // content: {
  //   // highlight: {
  //   //   // Theme used in all color schemes.
  //   //   theme: 'github-dark-high-contrast',
  //   //   // OR
  //   //   // theme: {
  //   //   //   // Default theme (same as single string)
  //   //   //   default: 'vitesse-dark',
  //   //   //   // Theme used if `html.dark`
  //   //   //   dark: 'github-dark-high-contrast',
  //   //   //   // Theme used if `html.sepia`
  //   //   //   sepia: 'monokai'
  //   //   // }
  //   // },
  // },

  runtimeConfig: {
    fromEmail: 'gomark.pro <<EMAIL>>',
    public: {
      brand: 'Gomark',
      contactEmail: '<EMAIL>',
      baseUrl: process.env.NODE_ENV === 'development' ? `http://localhost:${process.env.PORT ?? 3000}` : 'https://gomark.pro',
    },
  },

  // Build as SPA application
  // ssr: false,

  routeRules: {
    '/admin/**': { ssr: false },
    '/dashboard/**': { ssr: false },
    // '/api/**': { cors: true },
  },

  // hmr 的性能取决于 tailwindcss 的大小，而大小取决于页面使用 class 的多少以及 theme 的多少
  // 参考 nuxt-app，删除 ui 文件夹及 theme 等等，默认的 tailwindcss 仅 40 KB左右，hmr 就非常快
  css: ['~/assets/css/tailwind.css'],
  vite: {
    plugins: [
      tailwindcss(),
    ],
    server: {
      // 让 Vite dev server 对所有静态资源（包括 /_nuxt/assets/css/...）启用 gzip/br
      hmr: true,               // 保持原配置
      fs: { strict: true },    // 保持原配置
      middlewareMode: false,   // 保持原配置
      // 关键：打开压缩
      headers: {
        // 强制让 Vite 把 Accept-Encoding 传给内部压缩中间件
      },
      // Vite 4 支持 server.compress
      compress: true           // ✅ 只对 dev server 生效
    }
  },

  sourcemap: { server: false, client: false },

  nitro: {
    compressPublicAssets: true   // dev 阶段也会压缩
  },

  // 3️⃣ 阻止 @tailwindcss/vite 注入 sourcemap
  //    通过 vite 的 transformIndexHtml 钩子把注释去掉
  hooks: {
    'vite:extendConfig'(config) {
      config.plugins.push({
        name: 'strip-css-sourcemap',
        enforce: 'post',
        transform(code, id) {
          if (id.includes('tailwind.css') && id.endsWith('.css')) {
            return code.replace(/\/\*#\s*sourceMappingURL=.*\*\//gm, '')
          }
        }
      })
    }
  },

  // eslint: {
  //   config: {
  //     stylistic: true,
  //   },
  // },

  icon: {
    mode: 'svg',
    customCollections: [
      {
        prefix: 'custom',
        dir: './app/assets/custom-icons'
      },
    ],
  },
  
  shadcn: {
    prefix: '',
    componentDir: '@/components/ui',
  },
})
